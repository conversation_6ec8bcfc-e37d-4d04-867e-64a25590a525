# =========================================================================
# === Nav2 Parameters for Four-Wheeled Differential Drive Robot ===
# === Local Navigation Only (No SLAM Global Map Required) ===
# =========================================================================
# This configuration is optimized for local dynamic navigation using only:
# - LiDAR sensor data for obstacle detection
# - IMU for orientation
# - Odometry for position estimation
# Key features:
# - No AMCL (no global map localization)
# - Local costmap only navigation
# - DWB controller for differential drive kinematics
# - Obstacle avoidance based on real-time sensor data
# =========================================================================

# AMCL is disabled for local navigation - we don't need global localization
# amcl:
#   ros__parameters:
#     use_sim_time: True

bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: odom  # Changed from 'map' to 'odom' for local navigation
    robot_base_frame: base_link
    odom_topic: /odom_fused
    bt_loop_duration: 10
    default_server_timeout: 20
    # Use behavior tree optimized for local navigation without global map
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node

controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 50.0  # 提高控制频率以减少重影
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.001
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.5  # 增加容错性
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"]
    
    # 添加关键参数以提高响应性
    controller_server_timeout: 20.0
    
    # --- Key Change: Use DWB Controller for Differential Drive ---
    controller_plugins: ["FollowPath"]

    # Progress checker parameters - 优化以防止卡住
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.3  # 减少要求的移动半径
      movement_time_allowance: 15.0  # 增加时间允许

    # Goal checker parameters
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.20  # 稍微减少容忍度提高精度
      yaw_goal_tolerance: 1.5
      stateful: True

    # --- DWB Controller Configuration for Differential Drive ---
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: True  # 开启调试以观察轨迹评分
      min_vel_x: -0.3  # 允许倒退，适度限制倒退速度
      min_vel_y: 0.0
      max_vel_x: 0.5  # 降低最大线速度给转弯留余量
      max_vel_y: 0.0
      max_vel_theta: 1.8  # 降低最大角速度避免急摆
      min_speed_xy: 0.0
      max_speed_xy: 0.5
      min_speed_theta: 0.0
      # Acceleration limits - 优化路径跟随稳定性
      acc_lim_x: 1.5
      acc_lim_y: 0.0
      acc_lim_theta: 3.0  # 降低角加速度避免激进转弯
      decel_lim_x: -2.0  # 增加减速度遇障快停
      decel_lim_y: 0.0
      decel_lim_theta: -3.0
      # Trajectory generation - 优化路径跟随精度
      vx_samples: 18
      vy_samples: 1
      vth_samples: 50  # 增加角速度采样提高转弯精度
      sim_time: 2.5  # 增加仿真时间提前预测障碍和弯道
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.2
      xy_goal_tolerance: 0.15
      yaw_goal_tolerance: 0.15
      trans_stopped_velocity: 0.08
      short_circuit_trajectory_evaluation: False  # 完整评估所有轨迹
      stateful: True
      include_last_point: true
      allow_reversing: true  # 允许倒退轨迹采样
      # Critics - 重平衡权重优先路径跟随
      critics: ["BaseObstacle","PathAlign","PathDist","GoalAlign","GoalDist","RotateToGoal","Oscillation","TwirlingCritic","PreferForward"]
      BaseObstacle.scale: 0.5  # 提高障碍物权重确保避障
      BaseObstacle.sum_scores: false
      PathAlign.scale: 4.0  # 提高路径对齐权重
      PathAlign.forward_point_distance: 0.2
      PathAlign.aggregation_type: "last"
      PathDist.scale: 3.0  # 提高路径距离权重紧贴路径
      PathDist.aggregation_type: "last"
      GoalAlign.scale: 2.0  # 降低目标对齐避免过早切角
      GoalAlign.forward_point_distance: 0.1
      GoalAlign.aggregation_type: "last"
      GoalDist.scale: 1.0  # 降低目标距离权重
      GoalDist.aggregation_type: "last"
      RotateToGoal.scale: 1.0  # 降低转向目标权重
      RotateToGoal.slowing_factor: 2.0
      RotateToGoal.lookahead_time: 1.0
      Oscillation.scale: 0.5
      Oscillation.x_only_threshold: 0.05
      Oscillation.reset_dist: 0.25
      Oscillation.reset_angle: 0.2
      Oscillation.reset_time: -1.0
      TwirlingCritic.scale: 0.05
      PreferForward.scale: 0.3  # 适度前进偏好，允许必要时倒退
      PreferForward.strafe_x: 1.0
      PreferForward.strafe_theta: 1.0

planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: True

    # --- Use Hybrid A* (Smac) Planner for motion-feasible global paths ---
    planner_plugins: ["GridBased"]

    # SmacPlannerHybrid (Hybrid A*) Configuration - better with vehicle kinematics
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      tolerance: 0.5
      downsample_costmap: false
      downsampling_factor: 1
      allow_unknown: true
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 5.0
      motion_model_for_search: "DUBIN"
      angle_quantization_bins: 72
      analytic_expansion_ratio: 3.5
      analytic_expansion_max_length: 3.0
      minimum_turning_radius: 0.20  # reduced to avoid unnecessary big arcs
      reverse_penalty: 2.0
      change_penalty: 0.0
      non_straight_penalty: 0.7  # favor straighter paths when possible
      cost_penalty: 5.0  # 增加代价惩罚让规划器更重视避开高代价区域
      retrospective_penalty: 0.010
      lookup_table_size: 20.0
      cache_obstacle_heuristic: false
      debug_visualizations: false
      use_quadratic_cost_penalty: true  # 开启二次代价惩罚使规划器更避开障碍物
      downsample_obstacle_heuristic: true

# Global costmap configuration for planner_server (using odom frame)
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 5.0  # 提高全局代价地图更新频率
      publish_frequency: 2.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 6  # 减少尺寸提高性能
      height: 6
      resolution: 0.1
      track_unknown_space: false  # 不跟踪未知空间以提高性能
      
      # 添加关键参数
      lethal_cost_threshold: 100
      trinary_costmap: false

      # Robot footprint - 同步更新为缩短后车身尺寸
      footprint: "[[0.23, 0.16], [-0.23, 0.16], [-0.23, -0.16], [0.23, -0.16]]"  # 统一与局部代价地图
      footprint_padding: 0.02  # 额外安全缓冲 2cm

      plugins: ["obstacle_layer", "inflation_layer"]

      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 10.0  # 减少射线追踪范围
          raytrace_min_range: 0.15
          obstacle_max_range: 8.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 1.0  # 进一步降低衰减系数，使高代价区域更宽
        inflation_radius: 0.85  # 显著增大全局膨胀半径，确保路径远离障碍物

      always_send_full_costmap: True

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0  # 大幅提高更新频率以消除重影
      publish_frequency: 10.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 4  # 稍微减少窗口大小提高性能
      height: 4
      resolution: 0.05
      
      # 添加关键参数以减少重影
      track_unknown_space: false  # 不跟踪未知空间
      lethal_cost_threshold: 100  # 设置致命代价阈值
      trinary_costmap: false  # 使用连续代价地图

      # Robot footprint - 缩短车身后的准确外包络
      footprint: "[[0.23, 0.16], [-0.23, 0.16], [-0.23, -0.16], [0.23, -0.16]]"

      plugins: ["voxel_layer", "inflation_layer"]

      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 8.0  # 减少射线追踪范围提高性能
          raytrace_min_range: 0.15
          obstacle_max_range: 6.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15
          # 添加关键参数以减少重影
          clearing_threshold: 2  # 清除阈值
          marking_threshold: 0   # 标记阈值

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 2.5  # 稍微降低代价缩放
        inflation_radius: 0.4  # 减小膨胀半径允许更紧密通过
        inflate_unknown: false  # 不膨胀未知区域
        inflate_around_unknown: false  # 不在未知区域周围膨胀

      always_send_full_costmap: True

# Map server disabled for local navigation
# map_server:
#   ros__parameters:
#     use_sim_time: True

# Map saver disabled for local navigation
# map_saver:
#   ros__parameters:
#     use_sim_time: True

smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

behavior_server:
  ros__parameters:
    use_sim_time: True
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.1
    simulate_ahead_time: 2.0
    # Adjusted for differential drive robot with reverse capability
    max_rotational_vel: 2.5  # 增加最大旋转速度
    min_rotational_vel: 0.3
    rotational_acc_lim: 4.0  # 增加旋转加速度
    max_linear_vel: 0.6  # 降低最大线速度以配合转弯
    min_linear_vel: -0.5  # 允许倒退，与DWB控制器一致
    linear_acc_lim: 1.5

waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 50.0  # 大幅提高平滑频率以减少重影
    scale_velocities: false
    feedback: "OPEN_LOOP"
    # 优化速度限制以配合DWB控制器和倒退功能
    max_velocity: [0.6, 0.0, 2.5]  # 与DWB控制器保持一致，增加角速度
    min_velocity: [-0.5, 0.0, -2.5]  # 与DWB控制器保持一致，允许倒退
    max_accel: [1.5, 0.0, 4.0]  # 与DWB控制器保持一致，增加角加速度
    max_decel: [-1.5, 0.0, -4.0]
    odom_topic: "/odom_fused"
    odom_duration: 0.05  # 减少里程计持续时间提高响应性
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 0.5  # 减少超时时间