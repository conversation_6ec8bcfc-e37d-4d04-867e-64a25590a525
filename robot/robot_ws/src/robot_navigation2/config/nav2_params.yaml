# =========================================================================
# === Nav2 Parameters for Four-Wheeled Differential Drive Robot ===
# === Local Navigation Only (No SLAM Global Map Required) ===
# =========================================================================
# This configuration is optimized for local dynamic navigation using only:
# - LiDAR sensor data for obstacle detection
# - IMU for orientation
# - Odometry for position estimation
# Key features:
# - No AMCL (no global map localization)
# - Local costmap only navigation
# - DWB controller for differential drive kinematics
# - Obstacle avoidance based on real-time sensor data
# =========================================================================

# AMCL is disabled for local navigation - we don't need global localization
# amcl:
#   ros__parameters:
#     use_sim_time: True

bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: odom  # Changed from 'map' to 'odom' for local navigation
    robot_base_frame: base_link
    odom_topic: /odom_fused
    bt_loop_duration: 10
    default_server_timeout: 20
    # Use behavior tree optimized for local navigation without global map
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node

controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 50.0  # 提高控制频率以减少重影
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.001
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.5  # 增加容错性
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"]
    
    # 添加关键参数以提高响应性
    controller_server_timeout: 20.0
    
    # --- Key Change: Use DWB Controller for Differential Drive ---
    controller_plugins: ["FollowPath"]

    # Progress checker parameters - 优化以防止卡住
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.3  # 减少要求的移动半径
      movement_time_allowance: 15.0  # 增加时间允许

    # Goal checker parameters - 优化目标点接近行为
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.25  # 适度增加位置容忍度，避免过度精确导致转圈
      yaw_goal_tolerance: 0.3  # 大幅降低角度容忍度，减少转圈行为
      stateful: True

    # --- DWB Controller Configuration for Differential Drive ---
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: True  # 开启调试以观察轨迹评分
      min_vel_x: -0.2  # 适度限制倒退速度，避免过快倒退
      min_vel_y: 0.0   # 差速驱动无侧向运动
      max_vel_x: 0.4   # 降低最大线速度，提高控制精度
      max_vel_y: 0.0   # 差速驱动无侧向运动
      max_vel_theta: 1.5  # 适度降低最大角速度，避免急转
      min_speed_xy: 0.0
      max_speed_xy: 0.4   # 与max_vel_x保持一致
      min_speed_theta: 0.0
      # Acceleration limits - 优化四轮差速驱动稳定性
      acc_lim_x: 1.2      # 适度降低线加速度，提高稳定性
      acc_lim_y: 0.0      # 差速驱动无侧向加速度
      acc_lim_theta: 2.5  # 降低角加速度，避免激进转弯
      decel_lim_x: -1.8   # 适度减速度，平衡停车距离和稳定性
      decel_lim_y: 0.0    # 差速驱动无侧向减速度
      decel_lim_theta: -2.5  # 与角加速度对称
      # Trajectory generation - 优化四轮差速驱动路径跟随
      vx_samples: 20  # 增加线速度采样，更好适应不同速度需求
      vy_samples: 1   # 差速驱动无侧向运动
      vth_samples: 40  # 适度减少角速度采样，避免过度转弯选择
      sim_time: 2.0   # 适度减少仿真时间，提高响应性
      linear_granularity: 0.03  # 更细的线性粒度提高精度
      angular_granularity: 0.02  # 更细的角度粒度
      transform_tolerance: 0.15  # 降低变换容忍度提高精度
      xy_goal_tolerance: 0.12   # 降低位置容忍度
      yaw_goal_tolerance: 0.1   # 降低角度容忍度
      trans_stopped_velocity: 0.05  # 降低停止速度阈值
      short_circuit_trajectory_evaluation: False  # 完整评估所有轨迹
      stateful: True
      include_last_point: true
      allow_reversing: true  # 允许倒退轨迹采样
      # Critics - 重新优化权重，强化路径跟踪，减少不必要转弯
      critics: ["BaseObstacle","PathAlign","PathDist","GoalAlign","GoalDist","RotateToGoal","Oscillation","TwirlingCritic","PreferForward"]
      BaseObstacle.scale: 0.6  # 适度提高障碍物权重
      BaseObstacle.sum_scores: false
      PathAlign.scale: 6.0  # 大幅提高路径对齐权重，强制跟随路径
      PathAlign.forward_point_distance: 0.3  # 增加前瞻距离
      PathAlign.aggregation_type: "last"
      PathDist.scale: 5.0  # 大幅提高路径距离权重，紧贴路径
      PathDist.aggregation_type: "last"
      GoalAlign.scale: 1.5  # 降低目标对齐权重，避免过早偏离路径
      GoalAlign.forward_point_distance: 0.15
      GoalAlign.aggregation_type: "last"
      GoalDist.scale: 0.8  # 进一步降低目标距离权重
      GoalDist.aggregation_type: "last"
      RotateToGoal.scale: 0.5  # 大幅降低转向目标权重，减少转圈
      RotateToGoal.slowing_factor: 3.0  # 增加减速因子
      RotateToGoal.lookahead_time: 0.8  # 减少前瞻时间
      Oscillation.scale: 0.8  # 提高振荡惩罚
      Oscillation.x_only_threshold: 0.03  # 降低阈值，更敏感检测振荡
      Oscillation.reset_dist: 0.2
      Oscillation.reset_angle: 0.15
      Oscillation.reset_time: -1.0
      TwirlingCritic.scale: 0.1  # 提高旋转惩罚，减少转圈
      PreferForward.scale: 0.4  # 适度提高前进偏好
      PreferForward.strafe_x: 1.0
      PreferForward.strafe_theta: 1.0

planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: True

    # --- Use Hybrid A* (Smac) Planner for motion-feasible global paths ---
    planner_plugins: ["GridBased"]

    # SmacPlannerHybrid (Hybrid A*) Configuration - 优化四轮差速驱动避障
    GridBased:
      plugin: "nav2_smac_planner/SmacPlannerHybrid"
      tolerance: 0.3  # 降低容忍度提高路径精度
      downsample_costmap: false
      downsampling_factor: 1
      allow_unknown: true
      max_iterations: 1000000
      max_on_approach_iterations: 1000
      max_planning_time: 5.0
      motion_model_for_search: "DUBIN"  # 使用Dubin模型适合差速驱动
      angle_quantization_bins: 72
      analytic_expansion_ratio: 3.5
      analytic_expansion_max_length: 3.0
      minimum_turning_radius: 0.4  # 增大最小转弯半径，符合四轮差速驱动特性
      reverse_penalty: 1.5  # 适度降低倒退惩罚，允许必要时倒退
      change_penalty: 0.2  # 增加方向改变惩罚，偏好直线路径
      non_straight_penalty: 1.2  # 增加非直线惩罚，强烈偏好直线路径
      cost_penalty: 8.0  # 大幅增加代价惩罚，强制规划器远离障碍物
      retrospective_penalty: 0.015  # 稍微增加回溯惩罚
      lookup_table_size: 20.0
      cache_obstacle_heuristic: false
      debug_visualizations: false
      use_quadratic_cost_penalty: true  # 开启二次代价惩罚
      downsample_obstacle_heuristic: true
      # 新增参数优化避障行为
      smooth_path: true  # 开启路径平滑
      max_planning_time_ms: 5000.0  # 设置规划时间限制

# Global costmap configuration for planner_server (using odom frame)
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 5.0  # 提高全局代价地图更新频率
      publish_frequency: 2.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 6  # 减少尺寸提高性能
      height: 6
      resolution: 0.1
      track_unknown_space: false  # 不跟踪未知空间以提高性能
      
      # 添加关键参数
      lethal_cost_threshold: 100
      trinary_costmap: false

      # Robot footprint - 根据URDF实际尺寸：前后0.30m，左右0.30m
      footprint: "[[0.15, 0.15], [-0.15, 0.15], [-0.15, -0.15], [0.15, -0.15]]"  # 匹配实际车身尺寸
      footprint_padding: 0.03  # 增加安全缓冲至3cm

      plugins: ["obstacle_layer", "inflation_layer"]

      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 10.0  # 减少射线追踪范围
          raytrace_min_range: 0.15
          obstacle_max_range: 8.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 0.8  # 降低衰减系数，使高代价区域更宽更平缓
        inflation_radius: 1.0  # 进一步增大全局膨胀半径，强制路径远离障碍物
        inflate_unknown: false  # 不膨胀未知区域
        inflate_around_unknown: true  # 在未知区域周围膨胀以保持安全

      always_send_full_costmap: True

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0  # 大幅提高更新频率以消除重影
      publish_frequency: 10.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 4  # 稍微减少窗口大小提高性能
      height: 4
      resolution: 0.05
      
      # 添加关键参数以减少重影
      track_unknown_space: false  # 不跟踪未知空间
      lethal_cost_threshold: 100  # 设置致命代价阈值
      trinary_costmap: false  # 使用连续代价地图

      # Robot footprint - 与全局代价地图保持一致
      footprint: "[[0.15, 0.15], [-0.15, 0.15], [-0.15, -0.15], [0.15, -0.15]]"

      plugins: ["voxel_layer", "inflation_layer"]

      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 8.0  # 减少射线追踪范围提高性能
          raytrace_min_range: 0.15
          obstacle_max_range: 6.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15
          # 添加关键参数以减少重影
          clearing_threshold: 2  # 清除阈值
          marking_threshold: 0   # 标记阈值

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0  # 提高代价缩放，在局部规划中更敏感避障
        inflation_radius: 0.5  # 适度增加膨胀半径，平衡避障和通过性
        inflate_unknown: false  # 不膨胀未知区域
        inflate_around_unknown: true  # 在未知区域周围膨胀保持安全

      always_send_full_costmap: True

# Map server disabled for local navigation
# map_server:
#   ros__parameters:
#     use_sim_time: True

# Map saver disabled for local navigation
# map_saver:
#   ros__parameters:
#     use_sim_time: True

smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

behavior_server:
  ros__parameters:
    use_sim_time: True
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.1
    simulate_ahead_time: 2.0
    # 调整为四轮差速驱动机器人参数，与DWB控制器保持一致
    max_rotational_vel: 1.5   # 与DWB控制器保持一致
    min_rotational_vel: 0.2   # 降低最小旋转速度
    rotational_acc_lim: 2.5   # 与DWB控制器保持一致
    max_linear_vel: 0.4       # 与DWB控制器保持一致
    min_linear_vel: -0.2      # 与DWB控制器保持一致
    linear_acc_lim: 1.2       # 与DWB控制器保持一致

waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 50.0  # 大幅提高平滑频率以减少重影
    scale_velocities: false
    feedback: "OPEN_LOOP"
    # 优化速度限制以配合DWB控制器，匹配四轮差速驱动特性
    max_velocity: [0.4, 0.0, 1.5]   # 与DWB控制器保持一致
    min_velocity: [-0.2, 0.0, -1.5] # 与DWB控制器保持一致，允许适度倒退
    max_accel: [1.2, 0.0, 2.5]      # 与DWB控制器保持一致
    max_decel: [-1.8, 0.0, -2.5]    # 与DWB控制器保持一致
    odom_topic: "/odom_fused"
    odom_duration: 0.05  # 减少里程计持续时间提高响应性
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 0.5  # 减少超时时间