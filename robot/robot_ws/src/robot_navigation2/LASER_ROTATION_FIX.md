# 激光雷达旋转问题解决方案

## 问题描述
在进行局部导航时，机器人旋转时雷达扫描图也跟着旋转，这会导致：
1. 导航算法无法正确识别固定障碍物
2. 代价地图不稳定
3. 路径规划出现问题

## 问题原因
1. **激光雷达坐标系**: 激光雷达数据发布在`laser_front_link`和`laser_rear_link`坐标系下，这些坐标系随机器人旋转
2. **点云转换目标坐标系**: 原始的点云转激光扫描节点使用`base_footprint`作为目标坐标系，仍然会随机器人旋转
3. **RViz显示坐标系**: 如果RViz的固定坐标系设置不正确，会看到扫描图旋转

## 解决方案

### 方案1: 使用新的导航激光转换器（推荐）

我创建了一个新的`nav_laser_converter`节点，它会：
- 将激光扫描数据转换到固定的`odom`坐标系用于导航
- 同时提供在`base_link`坐标系的数据用于可视化

#### 启动方法：
```bash
# 启动机器人和传感器
ros2 launch robot_description robot.launch.py

# 启动新的激光转换器
ros2 launch pointcloud_merger nav_laser.launch.py

# 启动局部导航
ros2 launch robot_navigation2 pure_local_nav.launch.py
```

#### 话题说明：
- `/scan`: 用于导航的激光扫描（odom坐标系，不会旋转）
- `/scan_viz`: 用于可视化的激光扫描（base_link坐标系，跟随机器人）

### 方案2: 修改RViz显示设置

如果你想继续使用原有的激光扫描数据，可以在RViz中：

1. **设置固定坐标系**:
   - 在RViz的Global Options中
   - 将Fixed Frame设置为`odom`而不是`base_link`

2. **激光扫描显示设置**:
   - 在LaserScan显示项中
   - 确保"Use Fixed Frame"选项被勾选

### 方案3: 修改传感器配置

如果你想从源头解决问题，可以修改传感器插件配置：

在`gazebo_sensor_plugin.xacro`中，将激光雷达的frame_name改为固定坐标系：
```xml
<frame_name>odom</frame_name>  <!-- 而不是laser_front_link -->
```

但这种方法不推荐，因为会破坏TF树的正确性。

## 推荐使用方案1

方案1是最佳解决方案，因为：
1. **保持TF树正确性**: 传感器数据仍然在正确的坐标系中
2. **导航数据稳定**: 导航算法使用固定坐标系的数据
3. **可视化灵活**: 可以选择查看固定或跟随的激光扫描
4. **易于调试**: 两种数据都可用

## 验证解决方案

启动系统后，可以通过以下方式验证：

1. **检查话题**:
```bash
ros2 topic list | grep scan
# 应该看到 /scan 和 /scan_viz
```

2. **检查坐标系**:
```bash
ros2 topic echo /scan --once | grep frame_id
# 应该显示 frame_id: "odom"

ros2 topic echo /scan_viz --once | grep frame_id  
# 应该显示 frame_id: "base_link"
```

3. **在RViz中观察**:
   - 添加两个LaserScan显示
   - 一个订阅`/scan`（固定不旋转）
   - 一个订阅`/scan_viz`（跟随机器人旋转）

## 编译和安装

如果你修改了代码，需要重新编译：

```bash
cd robot/robot_ws
colcon build --packages-select pointcloud_merger
source install/setup.bash
```

这个解决方案确保了导航算法能够正确工作，同时保持了系统的灵活性和可调试性。
