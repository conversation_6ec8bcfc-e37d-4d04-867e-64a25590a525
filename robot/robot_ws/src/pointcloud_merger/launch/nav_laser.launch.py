from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        # 点云合并节点
        Node(
            package='pointcloud_merger',
            executable='pointcloud_merger_node',
            name='pointcloud_merger',
            output='screen',
            parameters=[{
            'use_sim_time': True
            }]
        ),
        
        # 导航激光转换节点（解决旋转问题）
        Node(
            package='pointcloud_merger',
            executable='nav_laser_converter',
            name='nav_laser_converter',
            output='screen',
            parameters=[{
                'angle_min': -3.14159,
                'angle_max': 3.14159,
                'angle_increment': 0.01,
                'range_min': 0.12,
                'range_max': 10.0,
                'nav_frame': 'odom',      # 导航用固定坐标系
                'viz_frame': 'base_link',  # 可视化用机器人坐标系
                'use_sim_time': True
            }]
        ),
    ])
