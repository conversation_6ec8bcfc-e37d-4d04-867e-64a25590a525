#include <memory>
#include <vector>
#include <cmath>
#include <limits>

// ROS2
#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"
#include "sensor_msgs/msg/laser_scan.hpp"

// PCL
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_types.h>
#include <pcl/point_cloud.h>

// TF2
#include <tf2_ros/transform_listener.h>
#include <tf2_ros/buffer.h>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <tf2_sensor_msgs/tf2_sensor_msgs.hpp>

class NavLaserConverter : public rclcpp::Node
{
public:
    NavLaserConverter()
    : Node("nav_laser_converter"),
      tf_buffer_(this->get_clock()),
      tf_listener_(tf_buffer_)
    {
        // QoS 设置为 Reliable
        rclcpp::QoS qos(rclcpp::KeepLast(10));
        qos.reliable();

        // 订阅合并后的点云
        pointcloud_subscriber_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "/scanp", qos,
            std::bind(&NavLaserConverter::pointcloud_callback, this, std::placeholders::_1));

        // 发布导航用的激光扫描（固定在odom坐标系）
        nav_scan_publisher_ = this->create_publisher<sensor_msgs::msg::LaserScan>(
            "/scan", qos);

        // 发布可视化用的激光扫描（跟随机器人）
        viz_scan_publisher_ = this->create_publisher<sensor_msgs::msg::LaserScan>(
            "/scan_viz", qos);

        // 参数设置
        this->declare_parameter<double>("angle_min", -3.14159);
        this->declare_parameter<double>("angle_max", 3.14159);
        this->declare_parameter<double>("angle_increment", 0.01);
        this->declare_parameter<double>("range_min", 0.12);
        this->declare_parameter<double>("range_max", 10.0);
        this->declare_parameter<std::string>("nav_frame", "odom");      // 导航用固定坐标系
        this->declare_parameter<std::string>("viz_frame", "base_link"); // 可视化用机器人坐标系

        RCLCPP_INFO(this->get_logger(), "Nav Laser Converter initialized");
    }

private:
    void pointcloud_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        // 获取参数
        std::string nav_frame = this->get_parameter("nav_frame").as_string();
        std::string viz_frame = this->get_parameter("viz_frame").as_string();
        
        // 转换为导航用激光扫描（固定坐标系）
        auto nav_scan = convert_to_laserscan(msg, nav_frame);
        if (nav_scan) {
            nav_scan_publisher_->publish(*nav_scan);
        }
        
        // 转换为可视化用激光扫描（机器人坐标系）
        auto viz_scan = convert_to_laserscan(msg, viz_frame);
        if (viz_scan) {
            viz_scan_publisher_->publish(*viz_scan);
        }
    }

    std::shared_ptr<sensor_msgs::msg::LaserScan> convert_to_laserscan(
        const sensor_msgs::msg::PointCloud2::SharedPtr& cloud_msg,
        const std::string& target_frame)
    {
        // 尝试进行坐标变换
        sensor_msgs::msg::PointCloud2 transformed_cloud;
        try
        {
            // 等待变换的可用性
            if (!tf_buffer_.canTransform(target_frame, cloud_msg->header.frame_id, 
                                       cloud_msg->header.stamp, rclcpp::Duration::from_seconds(0.1)))
            {
                RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                    "Cannot transform from %s to %s", cloud_msg->header.frame_id.c_str(), target_frame.c_str());
                return nullptr;
            }

            // 执行坐标变换
            tf2::doTransform(*cloud_msg, transformed_cloud, 
                           tf_buffer_.lookupTransform(target_frame, cloud_msg->header.frame_id, cloud_msg->header.stamp));
        }
        catch (tf2::TransformException &ex)
        {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 1000,
                "Transform failed: %s", ex.what());
            return nullptr;
        }

        // 转换 PointCloud2 到 PCL PointCloud
        pcl::PointCloud<pcl::PointXYZ>::Ptr pcl_cloud(new pcl::PointCloud<pcl::PointXYZ>());
        pcl::fromROSMsg(transformed_cloud, *pcl_cloud);

        // 获取参数
        double angle_min = this->get_parameter("angle_min").as_double();
        double angle_max = this->get_parameter("angle_max").as_double();
        double angle_increment = this->get_parameter("angle_increment").as_double();
        double range_min = this->get_parameter("range_min").as_double();
        double range_max = this->get_parameter("range_max").as_double();

        // 计算激光扫描的角度数量
        int num_readings = std::ceil((angle_max - angle_min) / angle_increment);

        // 初始化 LaserScan 消息
        auto scan = std::make_shared<sensor_msgs::msg::LaserScan>();
        scan->header.frame_id = target_frame;
        scan->header.stamp = this->get_clock()->now();
        scan->angle_min = angle_min;
        scan->angle_max = angle_max;
        scan->angle_increment = angle_increment;
        scan->time_increment = 0.0;
        scan->scan_time = 0.1; // 假设扫描时间为100ms
        scan->range_min = range_min;
        scan->range_max = range_max;
        scan->ranges.assign(num_readings, std::numeric_limits<float>::infinity());

        // 将点云转换为激光扫描
        for (const auto& point : pcl_cloud->points)
        {
            if (std::isnan(point.x) || std::isnan(point.y) || std::isnan(point.z))
                continue;

            double range = std::sqrt(point.x * point.x + point.y * point.y);
            if (range < range_min || range > range_max)
                continue;

            double angle = std::atan2(point.y, point.x);
            if (angle < angle_min || angle > angle_max)
                continue;

            int index = std::round((angle - angle_min) / angle_increment);
            if (index >= 0 && index < num_readings)
            {
                if (range < scan->ranges[index])
                {
                    scan->ranges[index] = range;
                }
            }
        }

        return scan;
    }

    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr pointcloud_subscriber_;
    rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr nav_scan_publisher_;
    rclcpp::Publisher<sensor_msgs::msg::LaserScan>::SharedPtr viz_scan_publisher_;
    
    tf2_ros::Buffer tf_buffer_;
    tf2_ros::TransformListener tf_listener_;
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<NavLaserConverter>());
    rclcpp::shutdown();
    return 0;
}
