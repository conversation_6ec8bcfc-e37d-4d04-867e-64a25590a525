// IMU + Wheel Odometry fusion node
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/joint_state.hpp"
#include "sensor_msgs/msg/imu.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "tf2_ros/transform_broadcaster.h"

class ImuWheelOdomNode : public rclcpp::Node {
public:
  ImuWheelOdomNode() : Node("imu_wheel_odom_node"), last_time_(0, 0, RCL_ROS_TIME) {
    // Parameters
    base_frame_id_ = declare_parameter<std::string>("base_frame_id", "base_footprint");
    odom_frame_id_ = declare_parameter<std::string>("odom_frame_id", "odom");
    odom_topic_ = declare_parameter<std::string>("odom_topic", "/odom_fused");
    publish_tf_ = declare_parameter<bool>("publish_tf", true);
    use_imu_yaw_rate_ = declare_parameter<bool>("use_imu_yaw_rate", true);
    publish_rate_ = declare_parameter<double>("publish_rate", 100.0);

    wheel_radius_ = declare_parameter<double>("wheel_radius", 0.08);
    wheel_separation_ = declare_parameter<double>("wheel_separation", 0.36);

    // Joint names (left and right sides)
    left_joints_ = declare_parameter<std::vector<std::string>>("left_wheel_joints",
      std::vector<std::string>{"front_left_wheel_joint", "rear_left_wheel_joint"});
    right_joints_ = declare_parameter<std::vector<std::string>>("right_wheel_joints",
      std::vector<std::string>{"front_right_wheel_joint", "rear_right_wheel_joint"});

    // Subscriptions
    auto qos_sensor = rclcpp::SensorDataQoS();
  joint_sub_ = create_subscription<sensor_msgs::msg::JointState>(
      "/joint_states", qos_sensor,
      std::bind(&ImuWheelOdomNode::on_joint, this, std::placeholders::_1));
    imu_sub_ = create_subscription<sensor_msgs::msg::Imu>(
      "/imu", qos_sensor,
      std::bind(&ImuWheelOdomNode::on_imu, this, std::placeholders::_1));

    // Publishers
    odom_pub_ = create_publisher<nav_msgs::msg::Odometry>(odom_topic_, 10);
    if (publish_tf_) tf_broadcaster_ = std::make_unique<tf2_ros::TransformBroadcaster>(*this);

    // Timer for integration loop
    using namespace std::chrono_literals;
    timer_ = create_wall_timer(std::chrono::duration<double>(1.0 / publish_rate_),
      std::bind(&ImuWheelOdomNode::on_timer, this));

    RCLCPP_INFO(get_logger(), "ImuWheelOdomNode started: publish_rate=%.1f, frames %s->%s",
      publish_rate_, odom_frame_id_.c_str(), base_frame_id_.c_str());
  }

private:
  // store last velocities
  std::unordered_map<std::string, double> last_joint_vel_;
  rclcpp::Time last_time_;
  double v_linear_{0.0};
  double w_omega_{0.0};
  bool imu_ready_{false};

  // pose state
  double x_{0.0}, y_{0.0}, yaw_{0.0};

  // params
  std::string base_frame_id_;
  std::string odom_frame_id_;
  std::string odom_topic_;
  bool publish_tf_;
  bool use_imu_yaw_rate_;
  double publish_rate_;
  double wheel_radius_;
  double wheel_separation_;
  std::vector<std::string> left_joints_;
  std::vector<std::string> right_joints_;

  // ROS interfaces
  rclcpp::Subscription<sensor_msgs::msg::JointState>::SharedPtr joint_sub_;
  rclcpp::Subscription<sensor_msgs::msg::Imu>::SharedPtr imu_sub_;
  rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr odom_pub_;
  std::unique_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_;
  rclcpp::TimerBase::SharedPtr timer_;

  void on_joint(const sensor_msgs::msg::JointState::SharedPtr msg) {
    // cache velocities by joint name
    for (size_t i = 0; i < msg->name.size(); ++i) {
      if (i < msg->velocity.size()) {
        last_joint_vel_[msg->name[i]] = msg->velocity[i];
      }
    }

    // compute linear velocity from left/right averages
    auto avg = [&](const std::vector<std::string>& names) {
      double sum = 0.0; int cnt = 0;
      for (const auto& n : names) {
        auto it = last_joint_vel_.find(n);
        if (it != last_joint_vel_.end()) { sum += it->second; cnt++; }
      }
      return cnt > 0 ? sum / cnt : 0.0;
    };
    double wl = avg(left_joints_);
    double wr = avg(right_joints_);
    // diff-drive kinematics
    v_linear_ = wheel_radius_ * (wr + wl) * 0.5;
    double w_kin = wheel_radius_ * (wr - wl) / wheel_separation_;
    if (!use_imu_yaw_rate_ || !imu_ready_) {
      w_omega_ = w_kin;
    }
  }

  void on_imu(const sensor_msgs::msg::Imu::SharedPtr msg) {
    // use yaw rate from IMU
    if (use_imu_yaw_rate_) {
      w_omega_ = msg->angular_velocity.z;
    }
    imu_ready_ = true;
  }

  void on_timer() {
    auto now = get_clock()->now();
    if (last_time_.nanoseconds() == 0) { last_time_ = now; return; }
    double dt = (now - last_time_).seconds();
    if (dt <= 0.0 || dt > 0.5) { last_time_ = now; return; } // guard

    // integrate pose
    x_ += v_linear_ * std::cos(yaw_) * dt;
    y_ += v_linear_ * std::sin(yaw_) * dt;
    yaw_ += w_omega_ * dt;

    // publish odom msg
    nav_msgs::msg::Odometry odom;
    odom.header.stamp = now;
    odom.header.frame_id = odom_frame_id_;
    odom.child_frame_id = base_frame_id_;
    odom.pose.pose.position.x = x_;
    odom.pose.pose.position.y = y_;
    tf2::Quaternion q; q.setRPY(0, 0, yaw_);
    odom.pose.pose.orientation = tf2::toMsg(q);
    odom.twist.twist.linear.x = v_linear_;
    odom.twist.twist.angular.z = w_omega_;
    // simple covariances
    odom.pose.covariance = {0.05,0,0,0,0,0,
                             0,0.05,0,0,0,0,
                             0,0,1e6,0,0,0,
                             0,0,0,1e6,0,0,
                             0,0,0,0,1e6,0,
                             0,0,0,0,0,0.1};
    odom.twist.covariance = {0.1,0,0,0,0,0,
                             0,0.1,0,0,0,0,
                             0,0,1e6,0,0,0,
                             0,0,0,1e6,0,0,
                             0,0,0,0,1e6,0,
                             0,0,0,0,0,0.1};
    odom_pub_->publish(odom);

    if (publish_tf_ && tf_broadcaster_) {
      geometry_msgs::msg::TransformStamped tf;
      tf.header.stamp = now;
      tf.header.frame_id = odom_frame_id_;
      tf.child_frame_id = base_frame_id_;
      tf.transform.translation.x = x_;
      tf.transform.translation.y = y_;
      tf.transform.translation.z = 0.0;
      tf.transform.rotation = tf2::toMsg(q);
      tf_broadcaster_->sendTransform(tf);
    }

    last_time_ = now;
  }
};

int main(int argc, char** argv) {
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<ImuWheelOdomNode>());
  rclcpp::shutdown();
  return 0;
}
