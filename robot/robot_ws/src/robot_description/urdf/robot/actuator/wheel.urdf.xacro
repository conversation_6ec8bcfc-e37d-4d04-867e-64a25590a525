<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    
    <!-- 轮子参数 -->
    <xacro:property name="wheel_radius" value="0.08"/> <!-- 轮子半径 8cm -->
    <xacro:property name="wheel_width" value="0.05"/>  <!-- 轮子宽度 5cm -->
    <xacro:property name="wheel_mass" value="1.0"/>    <!-- 轮子质量 1kg -->
    <!-- 车身长度已缩短为0.45，适当减小前后轮中心距，留出前后结构余量 -->
    <xacro:property name="wheel_offset_x" value="0.10"/> <!-- 前后轮距离中心的距离 (原0.22) -->
    <xacro:property name="wheel_offset_y" value="0.18"/> <!-- 左右轮距离中心的距离 -->
    <xacro:property name="wheel_offset_z" value="-0.05"/> <!-- 轮子相对base_link的高度偏移 -->

    <!-- 轮子宏定义 -->
    <xacro:macro name="wheel" params="prefix x_reflect y_reflect">
        
        <!-- 轮子链接 -->
        <link name="${prefix}_wheel_link">
            <inertial>
                <origin xyz="0 0 0" rpy="0 0 0"/>
                <mass value="${wheel_mass}"/>
                <inertia ixx="${wheel_mass/12*(3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                         ixy="0" ixz="0"
                         iyy="${wheel_mass/12*(3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                         iyz="0"
                         izz="${wheel_mass/2*wheel_radius*wheel_radius}"/>
            </inertial>
            
            <visual>
                <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
                </geometry>
                <material name="black">
                    <color rgba="0.2 0.2 0.2 1"/>
                </material>
            </visual>
            
            <collision>
                <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
                <geometry>
                    <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
                </geometry>
            </collision>
        </link>

        <!-- 轮子关节 -->
        <joint name="${prefix}_wheel_joint" type="continuous">
            <parent link="base_link"/>
            <child link="${prefix}_wheel_link"/>
            <origin xyz="${x_reflect*wheel_offset_x} ${y_reflect*wheel_offset_y} ${wheel_offset_z}" 
                    rpy="0 0 0"/>
            <axis xyz="0 1 0"/>
        </joint>

        <!-- Gazebo 轮子摩擦参数 -->
        <gazebo reference="${prefix}_wheel_link">
            <material>Gazebo/DarkGrey</material>
            <mu1>1.0</mu1>
            <mu2>0.5</mu2>
            <kp>1000000.0</kp>
            <kd>100.0</kd>
            <minDepth>0.001</minDepth>
            <maxVel>1.0</maxVel>
            <fdir1>1 0 0</fdir1>
        </gazebo>
        
    </xacro:macro>

    <!-- 创建四个轮子 -->
    <xacro:wheel prefix="front_left" x_reflect="1" y_reflect="1"/>
    <xacro:wheel prefix="front_right" x_reflect="1" y_reflect="-1"/>
    <xacro:wheel prefix="rear_left" x_reflect="-1" y_reflect="1"/>
    <xacro:wheel prefix="rear_right" x_reflect="-1" y_reflect="-1"/>
    
    <!-- ros2_control 硬件接口 -->
    <ros2_control name="GazeboSystem" type="system">
        <hardware>
            <plugin>gazebo_ros2_control/GazeboSystem</plugin>
        </hardware>
        
        <!-- 前左轮 -->
        <joint name="front_left_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-10</param>
                <param name="max">10</param>
            </command_interface>
            <state_interface name="velocity"/>
            <state_interface name="position"/>
        </joint>
        
        <!-- 前右轮 -->
        <joint name="front_right_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-10</param>
                <param name="max">10</param>
            </command_interface>
            <state_interface name="velocity"/>
            <state_interface name="position"/>
        </joint>
        
        <!-- 后左轮 -->
        <joint name="rear_left_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-10</param>
                <param name="max">10</param>
            </command_interface>
            <state_interface name="velocity"/>
            <state_interface name="position"/>
        </joint>
        
        <!-- 后右轮 -->
        <joint name="rear_right_wheel_joint">
            <command_interface name="velocity">
                <param name="min">-10</param>
                <param name="max">10</param>
            </command_interface>
            <state_interface name="velocity"/>
            <state_interface name="position"/>
        </joint>
    </ros2_control>

    <!-- Gazebo ros2_control 插件 -->
    <gazebo>
        <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
            <parameters>$(find robot_description)/config/ros2_control.yaml</parameters>
        </plugin>
    </gazebo>
    
</robot>