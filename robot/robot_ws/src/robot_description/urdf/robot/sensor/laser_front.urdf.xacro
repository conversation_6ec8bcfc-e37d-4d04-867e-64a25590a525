<robot xmlns:xacro="http://www.ros.org/wiki/xacro">
    <link name="laser_front_link">
        <visual>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <cylinder length="0.05" radius="0.02" />
            </geometry>
            <material name="red">
                <color rgba="1 0 0 1" />
            </material>
        </visual>
        <collision>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <geometry>
                <cylinder length="0.05" radius="0.02" />
            </geometry>
        </collision>
        <inertial>
            <origin xyz="0 0 0" rpy="0 0 0" />
            <mass value="0.1" />
            <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001" />
        </inertial>
    </link>

    <joint name="laser_front_joint" type="fixed">
        <parent link="base_link" />
        <child link="laser_front_link" />
        <origin xyz="0.13 0 0.07" rpy="0 0 0" /> <!-- 车身顶部前端 -->
    </joint>
</robot>
