controller_manager:
  ros__parameters:
    update_rate: 100  # Hz

    # --- 控制器列表 ---
    # 1. 差速驱动控制器
    diff_drive_controller:
      type: diff_drive_controller/DiffDriveController

    # 2. 关节状态发布器 (确保这个控制器也在列表中)
    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

# =======================================================
# ===           差速驱动控制器详细参数配置            ===
# =======================================================
diff_drive_controller:
  ros__parameters:
    # --- 高频率更新以确保TF变换精度 ---
    publish_rate: 200.0  # 提高到200Hz以减少旋转重影
    base_frame_id: base_footprint
    odom_frame_id: odom
    enable_odom_tf: false
    
    # --- 轮子配置 ---
    left_wheel_names: ["front_left_wheel_joint","rear_left_wheel_joint"]
    right_wheel_names: ["front_right_wheel_joint","rear_right_wheel_joint"]

    wheel_separation: 0.36   # 2 * wheel_offset_y = 2 * 0.18
    wheel_radius: 0.08       # wheel_radius
    
    # --- 速度命令配置 ---
    use_stamped_vel: false   # 监听 geometry_msgs/msg/Twist
    cmd_vel_timeout: 0.5
    
    # --- 关键优化参数：消除旋转重影 ---
    # 1. 提高里程计发布频率和质量
    odom_topic: "odom"
    cmd_vel_topic: "cmd_vel"
    
    # 2. TF发布优化
    tf_frame_prefix_enable: false
    tf_frame_prefix: ""
    
    # 3. 里程计协方差矩阵优化（降低不确定性）
    pose_covariance_diagonal: [0.001, 0.001, 0.001, 0.001, 0.001, 0.01]
    twist_covariance_diagonal: [0.001, 0.001, 0.001, 0.001, 0.001, 0.01]
    
    # 4. 轮子编码器校准参数
    wheel_separation_multiplier: 1.0
    left_wheel_radius_multiplier: 1.0  
    right_wheel_radius_multiplier: 1.0
    
    # 5. 开环/闭环控制优化
    open_loop: false  # 使用闭环控制提高精度

# =======================================================
# ===         关节状态发布器 (通常无需配置)           ===
# =======================================================
joint_state_broadcaster:
  ros__parameters:
    {}