# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/robot/robot_ws/src/pointcloud_merger

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/robot/robot_ws/build/pointcloud_merger

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/robot/robot_ws/build/pointcloud_merger/CMakeFiles /home/<USER>/robot/robot_ws/build/pointcloud_merger//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/robot/robot_ws/build/pointcloud_merger/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named pointcloud_merger_uninstall

# Build rule for target.
pointcloud_merger_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 pointcloud_merger_uninstall
.PHONY : pointcloud_merger_uninstall

# fast build rule for target.
pointcloud_merger_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_merger_uninstall.dir/build.make CMakeFiles/pointcloud_merger_uninstall.dir/build
.PHONY : pointcloud_merger_uninstall/fast

#=============================================================================
# Target rules for targets named pointcloud_merger_node

# Build rule for target.
pointcloud_merger_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 pointcloud_merger_node
.PHONY : pointcloud_merger_node

# fast build rule for target.
pointcloud_merger_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_merger_node.dir/build.make CMakeFiles/pointcloud_merger_node.dir/build
.PHONY : pointcloud_merger_node/fast

#=============================================================================
# Target rules for targets named pointcloud_to_laserscan_node

# Build rule for target.
pointcloud_to_laserscan_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 pointcloud_to_laserscan_node
.PHONY : pointcloud_to_laserscan_node

# fast build rule for target.
pointcloud_to_laserscan_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_to_laserscan_node.dir/build.make CMakeFiles/pointcloud_to_laserscan_node.dir/build
.PHONY : pointcloud_to_laserscan_node/fast

#=============================================================================
# Target rules for targets named nav_laser_converter

# Build rule for target.
nav_laser_converter: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nav_laser_converter
.PHONY : nav_laser_converter

# fast build rule for target.
nav_laser_converter/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nav_laser_converter.dir/build.make CMakeFiles/nav_laser_converter.dir/build
.PHONY : nav_laser_converter/fast

#=============================================================================
# Target rules for targets named imu_wheel_odom_node

# Build rule for target.
imu_wheel_odom_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 imu_wheel_odom_node
.PHONY : imu_wheel_odom_node

# fast build rule for target.
imu_wheel_odom_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/imu_wheel_odom_node.dir/build.make CMakeFiles/imu_wheel_odom_node.dir/build
.PHONY : imu_wheel_odom_node/fast

src/imu_wheel_odom_node.o: src/imu_wheel_odom_node.cpp.o
.PHONY : src/imu_wheel_odom_node.o

# target to build an object file
src/imu_wheel_odom_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/imu_wheel_odom_node.dir/build.make CMakeFiles/imu_wheel_odom_node.dir/src/imu_wheel_odom_node.cpp.o
.PHONY : src/imu_wheel_odom_node.cpp.o

src/imu_wheel_odom_node.i: src/imu_wheel_odom_node.cpp.i
.PHONY : src/imu_wheel_odom_node.i

# target to preprocess a source file
src/imu_wheel_odom_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/imu_wheel_odom_node.dir/build.make CMakeFiles/imu_wheel_odom_node.dir/src/imu_wheel_odom_node.cpp.i
.PHONY : src/imu_wheel_odom_node.cpp.i

src/imu_wheel_odom_node.s: src/imu_wheel_odom_node.cpp.s
.PHONY : src/imu_wheel_odom_node.s

# target to generate assembly for a file
src/imu_wheel_odom_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/imu_wheel_odom_node.dir/build.make CMakeFiles/imu_wheel_odom_node.dir/src/imu_wheel_odom_node.cpp.s
.PHONY : src/imu_wheel_odom_node.cpp.s

src/nav_laser_converter.o: src/nav_laser_converter.cpp.o
.PHONY : src/nav_laser_converter.o

# target to build an object file
src/nav_laser_converter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nav_laser_converter.dir/build.make CMakeFiles/nav_laser_converter.dir/src/nav_laser_converter.cpp.o
.PHONY : src/nav_laser_converter.cpp.o

src/nav_laser_converter.i: src/nav_laser_converter.cpp.i
.PHONY : src/nav_laser_converter.i

# target to preprocess a source file
src/nav_laser_converter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nav_laser_converter.dir/build.make CMakeFiles/nav_laser_converter.dir/src/nav_laser_converter.cpp.i
.PHONY : src/nav_laser_converter.cpp.i

src/nav_laser_converter.s: src/nav_laser_converter.cpp.s
.PHONY : src/nav_laser_converter.s

# target to generate assembly for a file
src/nav_laser_converter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/nav_laser_converter.dir/build.make CMakeFiles/nav_laser_converter.dir/src/nav_laser_converter.cpp.s
.PHONY : src/nav_laser_converter.cpp.s

src/pointcloud_merger_node.o: src/pointcloud_merger_node.cpp.o
.PHONY : src/pointcloud_merger_node.o

# target to build an object file
src/pointcloud_merger_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_merger_node.dir/build.make CMakeFiles/pointcloud_merger_node.dir/src/pointcloud_merger_node.cpp.o
.PHONY : src/pointcloud_merger_node.cpp.o

src/pointcloud_merger_node.i: src/pointcloud_merger_node.cpp.i
.PHONY : src/pointcloud_merger_node.i

# target to preprocess a source file
src/pointcloud_merger_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_merger_node.dir/build.make CMakeFiles/pointcloud_merger_node.dir/src/pointcloud_merger_node.cpp.i
.PHONY : src/pointcloud_merger_node.cpp.i

src/pointcloud_merger_node.s: src/pointcloud_merger_node.cpp.s
.PHONY : src/pointcloud_merger_node.s

# target to generate assembly for a file
src/pointcloud_merger_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_merger_node.dir/build.make CMakeFiles/pointcloud_merger_node.dir/src/pointcloud_merger_node.cpp.s
.PHONY : src/pointcloud_merger_node.cpp.s

src/pointcloud_to_laserscan_node.o: src/pointcloud_to_laserscan_node.cpp.o
.PHONY : src/pointcloud_to_laserscan_node.o

# target to build an object file
src/pointcloud_to_laserscan_node.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_to_laserscan_node.dir/build.make CMakeFiles/pointcloud_to_laserscan_node.dir/src/pointcloud_to_laserscan_node.cpp.o
.PHONY : src/pointcloud_to_laserscan_node.cpp.o

src/pointcloud_to_laserscan_node.i: src/pointcloud_to_laserscan_node.cpp.i
.PHONY : src/pointcloud_to_laserscan_node.i

# target to preprocess a source file
src/pointcloud_to_laserscan_node.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_to_laserscan_node.dir/build.make CMakeFiles/pointcloud_to_laserscan_node.dir/src/pointcloud_to_laserscan_node.cpp.i
.PHONY : src/pointcloud_to_laserscan_node.cpp.i

src/pointcloud_to_laserscan_node.s: src/pointcloud_to_laserscan_node.cpp.s
.PHONY : src/pointcloud_to_laserscan_node.s

# target to generate assembly for a file
src/pointcloud_to_laserscan_node.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_to_laserscan_node.dir/build.make CMakeFiles/pointcloud_to_laserscan_node.dir/src/pointcloud_to_laserscan_node.cpp.s
.PHONY : src/pointcloud_to_laserscan_node.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... pointcloud_merger_uninstall"
	@echo "... uninstall"
	@echo "... imu_wheel_odom_node"
	@echo "... nav_laser_converter"
	@echo "... pointcloud_merger_node"
	@echo "... pointcloud_to_laserscan_node"
	@echo "... src/imu_wheel_odom_node.o"
	@echo "... src/imu_wheel_odom_node.i"
	@echo "... src/imu_wheel_odom_node.s"
	@echo "... src/nav_laser_converter.o"
	@echo "... src/nav_laser_converter.i"
	@echo "... src/nav_laser_converter.s"
	@echo "... src/pointcloud_merger_node.o"
	@echo "... src/pointcloud_merger_node.i"
	@echo "... src/pointcloud_merger_node.s"
	@echo "... src/pointcloud_to_laserscan_node.o"
	@echo "... src/pointcloud_to_laserscan_node.i"
	@echo "... src/pointcloud_to_laserscan_node.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

