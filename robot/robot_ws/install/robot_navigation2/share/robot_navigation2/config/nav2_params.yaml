# =========================================================================
# === Nav2 Parameters for Four-Wheeled Differential Drive Robot ===
# === Local Navigation Only (No SLAM Global Map Required) ===
# =========================================================================
# This configuration is optimized for local dynamic navigation using only:
# - LiDAR sensor data for obstacle detection
# - IMU for orientation
# - Odometry for position estimation
# Key features:
# - No AMCL (no global map localization)
# - Local costmap only navigation
# - DWB controller for differential drive kinematics
# - Obstacle avoidance based on real-time sensor data
# =========================================================================

# AMCL is disabled for local navigation - we don't need global localization
# amcl:
#   ros__parameters:
#     use_sim_time: True

bt_navigator:
  ros__parameters:
    use_sim_time: True
    global_frame: odom  # Changed from 'map' to 'odom' for local navigation
    robot_base_frame: base_link
    odom_topic: /odom_fused
    bt_loop_duration: 10
    default_server_timeout: 20
    # Use behavior tree optimized for local navigation without global map
    plugin_lib_names:
      - nav2_compute_path_to_pose_action_bt_node
      - nav2_compute_path_through_poses_action_bt_node
      - nav2_smooth_path_action_bt_node
      - nav2_follow_path_action_bt_node
      - nav2_spin_action_bt_node
      - nav2_wait_action_bt_node
      - nav2_assisted_teleop_action_bt_node
      - nav2_back_up_action_bt_node
      - nav2_drive_on_heading_bt_node
      - nav2_clear_costmap_service_bt_node
      - nav2_is_stuck_condition_bt_node
      - nav2_goal_reached_condition_bt_node
      - nav2_goal_updated_condition_bt_node
      - nav2_globally_updated_goal_condition_bt_node
      - nav2_is_path_valid_condition_bt_node
      - nav2_initial_pose_received_condition_bt_node
      - nav2_reinitialize_global_localization_service_bt_node
      - nav2_rate_controller_bt_node
      - nav2_distance_controller_bt_node
      - nav2_speed_controller_bt_node
      - nav2_truncate_path_action_bt_node
      - nav2_truncate_path_local_action_bt_node
      - nav2_goal_updater_node_bt_node
      - nav2_recovery_node_bt_node
      - nav2_pipeline_sequence_bt_node
      - nav2_round_robin_node_bt_node
      - nav2_transform_available_condition_bt_node
      - nav2_time_expired_condition_bt_node
      - nav2_path_expiring_timer_condition
      - nav2_distance_traveled_condition_bt_node
      - nav2_single_trigger_bt_node
      - nav2_goal_updated_controller_bt_node
      - nav2_is_battery_low_condition_bt_node
      - nav2_navigate_through_poses_action_bt_node
      - nav2_navigate_to_pose_action_bt_node
      - nav2_remove_passed_goals_action_bt_node
      - nav2_planner_selector_bt_node
      - nav2_controller_selector_bt_node
      - nav2_goal_checker_selector_bt_node
      - nav2_controller_cancel_bt_node
      - nav2_path_longer_on_approach_bt_node
      - nav2_wait_cancel_bt_node
      - nav2_spin_cancel_bt_node
      - nav2_back_up_cancel_bt_node
      - nav2_assisted_teleop_cancel_bt_node
      - nav2_drive_on_heading_cancel_bt_node
      - nav2_is_battery_charging_condition_bt_node

controller_server:
  ros__parameters:
    use_sim_time: True
    controller_frequency: 50.0  # 提高控制频率以减少重影
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.001
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.5  # 增加容错性
    progress_checker_plugin: "progress_checker"
    goal_checker_plugins: ["goal_checker"]
    
    # 添加关键参数以提高响应性
    controller_server_timeout: 20.0
    
    # --- Key Change: Use DWB Controller for Differential Drive ---
    controller_plugins: ["FollowPath"]

    # Progress checker parameters - 优化以防止卡住
    progress_checker:
      plugin: "nav2_controller::SimpleProgressChecker"
      required_movement_radius: 0.3  # 减少要求的移动半径
      movement_time_allowance: 15.0  # 增加时间允许

    # Goal checker parameters
    goal_checker:
      plugin: "nav2_controller::SimpleGoalChecker"
      xy_goal_tolerance: 0.20  # 稍微减少容忍度提高精度
      yaw_goal_tolerance: 0.20
      stateful: True

    # --- DWB Controller Configuration for Differential Drive ---
    FollowPath:
      plugin: "dwb_core::DWBLocalPlanner"
      debug_trajectory_details: False
      min_vel_x: -0.5  # 允许倒退，负值表示可以向后移动
      min_vel_y: 0.0
      max_vel_x: 0.6  # 降低最大线速度以允许更小转弯半径
      max_vel_y: 0.0
      max_vel_theta: 2.5  # 增加最大角速度以实现更小转弯半径
      min_speed_xy: 0.0
      max_speed_xy: 0.6
      min_speed_theta: 0.0
      # Acceleration limits - 优化以减少重影
      acc_lim_x: 1.5  # 降低线加速度以更好控制
      acc_lim_y: 0.0
      acc_lim_theta: 4.0  # 增加角加速度以快速转向
      decel_lim_x: -1.5
      decel_lim_y: 0.0
      decel_lim_theta: -4.0
      # Trajectory generation - 优化采样支持倒退
      vx_samples: 20  # 增加线速度采样以包含倒退选项
      vy_samples: 1
      vth_samples: 40  # 增加角速度采样以更好探索转弯选项
      sim_time: 1.5  # 减少仿真时间以允许更激进的转弯
      linear_granularity: 0.03  # 更细的线性粒度
      angular_granularity: 0.02  # 更细的角度粒度以精确转弯
      transform_tolerance: 0.1  # 降低变换容忍度提高精度
      xy_goal_tolerance: 0.15  # 减少位置容忍度
      yaw_goal_tolerance: 0.15  # 减少角度容忍度
      trans_stopped_velocity: 0.1  # 降低停止速度阈值
      short_circuit_trajectory_evaluation: True
      stateful: True
      # 倒退相关参数
      include_last_point: true  # 包含路径最后一点
      allow_reversing: true  # 明确允许倒退
      # Critics (cost functions) - 重新调整权重以支持倒退和小转弯半径
      critics: ["RotateToGoal", "Oscillation", "BaseObstacle", "GoalAlign", "PathAlign", "PathDist", "GoalDist", "PreferForward"]
      BaseObstacle.scale: 0.02  # 降低障碍物权重以允许更接近转弯
      PathAlign.scale: 15.0  # 降低路径对齐权重以允许偏离
      PathAlign.forward_point_distance: 0.1  # 减少前瞻距离
      GoalAlign.scale: 35.0  # 增加目标对齐权重以快速转向目标
      GoalAlign.forward_point_distance: 0.1  # 减少前瞻距离
      PathDist.scale: 20.0  # 降低路径距离权重
      GoalDist.scale: 40.0  # 增加目标距离权重以快速到达
      RotateToGoal.scale: 30.0  # 降低转向目标权重以允许倒退
      RotateToGoal.slowing_factor: 2.0  # 降低减速因子以快速转向
      RotateToGoal.lookahead_time: -1.0
      # 倒退偏好设置
      PreferForward.scale: 0.5  # 降低前进偏好，允许倒退
      PreferForward.strafe_x: 1.0
      PreferForward.strafe_theta: 1.0

planner_server:
  ros__parameters:
    expected_planner_frequency: 20.0
    use_sim_time: True

    # --- Use NavFn Planner for Local Navigation ---
    planner_plugins: ["GridBased"]

    # NavFn Planner Configuration - suitable for local navigation
    GridBased:
      plugin: "nav2_navfn_planner/NavfnPlanner"
      tolerance: 0.5
      use_astar: false  # Use Dijkstra for more conservative paths
      allow_unknown: true  # Allow planning through unknown areas

# Global costmap configuration for planner_server (using odom frame)
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 5.0  # 提高全局代价地图更新频率
      publish_frequency: 2.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 8  # 减少尺寸提高性能
      height: 8
      resolution: 0.1
      track_unknown_space: false  # 不跟踪未知空间以提高性能
      
      # 添加关键参数
      lethal_cost_threshold: 100
      trinary_costmap: false

      # Robot footprint - same as local costmap
      footprint: "[[0.28, 0.16], [-0.28, 0.16], [-0.28, -0.16], [0.28, -0.16]]"

      plugins: ["obstacle_layer", "inflation_layer"]

      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: True
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 10.0  # 减少射线追踪范围
          raytrace_min_range: 0.15
          obstacle_max_range: 8.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 3.0
        inflation_radius: 0.6

      always_send_full_costmap: True

local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 20.0  # 大幅提高更新频率以消除重影
      publish_frequency: 10.0  # 提高发布频率
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: True
      rolling_window: true
      width: 5  # 稍微减少窗口大小提高性能
      height: 5
      resolution: 0.05
      
      # 添加关键参数以减少重影
      track_unknown_space: false  # 不跟踪未知空间
      lethal_cost_threshold: 100  # 设置致命代价阈值
      trinary_costmap: false  # 使用连续代价地图

      # Robot footprint based on your URDF dimensions
      # Body: 0.55m length x 0.3m width (from base.urdf.xacro)
      # Slightly reduced margin for tighter turning
      footprint: "[[0.28, 0.16], [-0.28, 0.16], [-0.28, -0.16], [0.28, -0.16]]"

      plugins: ["voxel_layer", "inflation_layer"]

      voxel_layer:
        plugin: "nav2_costmap_2d::VoxelLayer"
        enabled: True
        publish_voxel_map: True
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 2.0
        mark_threshold: 0
        observation_sources: scan
        scan:
          topic: /scan  # 使用odom坐标系固定的激光扫描
          max_obstacle_height: 2.0
          clearing: True
          marking: True
          data_type: "LaserScan"
          raytrace_max_range: 8.0  # 减少射线追踪范围提高性能
          raytrace_min_range: 0.15
          obstacle_max_range: 6.0  # 减少障碍物检测范围
          obstacle_min_range: 0.15
          # 添加关键参数以减少重影
          clearing_threshold: 2  # 清除阈值
          marking_threshold: 0   # 标记阈值

      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        cost_scaling_factor: 2.5  # 稍微降低代价缩放
        inflation_radius: 0.5  # 减少膨胀半径以减少重影
        inflate_unknown: false  # 不膨胀未知区域
        inflate_around_unknown: false  # 不在未知区域周围膨胀

      always_send_full_costmap: True

# Map server disabled for local navigation
# map_server:
#   ros__parameters:
#     use_sim_time: True

# Map saver disabled for local navigation
# map_saver:
#   ros__parameters:
#     use_sim_time: True

smoother_server:
  ros__parameters:
    use_sim_time: True
    smoother_plugins: ["simple_smoother"]
    simple_smoother:
      plugin: "nav2_smoother::SimpleSmoother"
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: True

behavior_server:
  ros__parameters:
    use_sim_time: True
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins: ["spin", "backup", "drive_on_heading", "wait"]
    spin:
      plugin: "nav2_behaviors/Spin"
    backup:
      plugin: "nav2_behaviors/BackUp"
    drive_on_heading:
      plugin: "nav2_behaviors/DriveOnHeading"
    wait:
      plugin: "nav2_behaviors/Wait"
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.1
    simulate_ahead_time: 2.0
    # Adjusted for differential drive robot with reverse capability
    max_rotational_vel: 2.5  # 增加最大旋转速度
    min_rotational_vel: 0.3
    rotational_acc_lim: 4.0  # 增加旋转加速度
    max_linear_vel: 0.6  # 降低最大线速度以配合转弯
    min_linear_vel: -0.5  # 允许倒退，与DWB控制器一致
    linear_acc_lim: 1.5

waypoint_follower:
  ros__parameters:
    use_sim_time: True
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: "wait_at_waypoint"
    wait_at_waypoint:
      plugin: "nav2_waypoint_follower::WaitAtWaypoint"
      enabled: True
      waypoint_pause_duration: 200

velocity_smoother:
  ros__parameters:
    use_sim_time: True
    smoothing_frequency: 50.0  # 大幅提高平滑频率以减少重影
    scale_velocities: false
    feedback: "OPEN_LOOP"
    # 优化速度限制以配合DWB控制器和倒退功能
    max_velocity: [0.6, 0.0, 2.5]  # 与DWB控制器保持一致，增加角速度
    min_velocity: [-0.5, 0.0, -2.5]  # 与DWB控制器保持一致，允许倒退
    max_accel: [1.5, 0.0, 4.0]  # 与DWB控制器保持一致，增加角加速度
    max_decel: [-1.5, 0.0, -4.0]
    odom_topic: "/odom_fused"
    odom_duration: 0.05  # 减少里程计持续时间提高响应性
    deadband_velocity: [0.0, 0.0, 0.0]
    velocity_timeout: 0.5  # 减少超时时间