from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    use_sim_time = LaunchConfiguration('use_sim_time')

    return LaunchDescription([
        DeclareLaunchArgument('use_sim_time', default_value='true'),

        Node(
            package='pointcloud_merger',
            executable='imu_wheel_odom_node',
            name='imu_wheel_odom',
            output='screen',
            parameters=[{
                'use_sim_time': use_sim_time,
                'base_frame_id': 'base_footprint',
                'odom_frame_id': 'odom',
                'odom_topic': '/odom_fused',
                'publish_tf': True,
                'use_imu_yaw_rate': True,
                'publish_rate': 100.0,
                'wheel_radius': 0.08,
                'wheel_separation': 0.36,
                'left_wheel_joints': ['front_left_wheel_joint','rear_left_wheel_joint'],
                'right_wheel_joints': ['front_right_wheel_joint','rear_right_wheel_joint'],
            }]
        )
    ])
