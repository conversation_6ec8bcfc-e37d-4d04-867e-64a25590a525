[0.009s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.039s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.047s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.055s] -- Install configuration: ""
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/local_navigation.launch.py
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/navigation2.launch.py
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.pgm
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.yaml
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config
[0.057s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/nav2_params.yaml
[0.058s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/local_nav_rviz.rviz
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/package_run_dependencies/robot_navigation2
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/parent_prefix_path/robot_navigation2
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.sh
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.dsv
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.sh
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.dsv
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.bash
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.sh
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.zsh
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.dsv
[0.059s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/packages/robot_navigation2
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config.cmake
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config-version.cmake
[0.059s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.xml
[0.069s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
