[0.010s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.053s] [ 25%] Built target imu_wheel_odom_node
[0.066s] [ 50%] Built target pointcloud_to_laserscan_node
[0.066s] [100%] Built target nav_laser_converter
[0.066s] [100%] Built target pointcloud_merger_node
[0.081s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.081s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.089s] -- Install configuration: ""
[0.089s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node
[0.089s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node
[0.089s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/nav_laser_converter
[0.089s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/imu_wheel_odom_node
[0.089s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/nav_laser.launch.py
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/imu_wheel_odom.launch.py
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv
[0.090s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake
[0.090s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake
[0.091s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml
[0.092s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
