[0.011s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.063s] [ 25%] Built target nav_laser_converter
[0.065s] [ 50%] Built target pointcloud_to_laserscan_node
[0.066s] [ 75%] Built target imu_wheel_odom_node
[0.067s] [100%] Built target pointcloud_merger_node
[0.085s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.087s] Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.094s] -- Install configuration: ""
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/nav_laser_converter
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/imu_wheel_odom_node
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch
[0.094s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/nav_laser.launch.py
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/imu_wheel_odom.launch.py
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv
[0.095s] -- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake
[0.095s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake
[0.096s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml
[0.097s] Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
