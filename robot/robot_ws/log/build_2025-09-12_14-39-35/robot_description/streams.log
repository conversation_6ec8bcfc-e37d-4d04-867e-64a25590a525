[0.008s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.046s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.047s] Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.052s] -- Install configuration: ""
[0.053s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/package_run_dependencies/robot_description
[0.053s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/parent_prefix_path/robot_description
[0.053s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.sh
[0.053s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.dsv
[0.053s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.sh
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.dsv
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.bash
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.sh
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.zsh
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.dsv
[0.054s] -- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/packages/robot_description
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig.cmake
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig-version.cmake
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.xml
[0.054s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch/gazebo.launch.py
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/robot.urdf.xacro
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator/wheel.urdf.xacro
[0.055s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/base.urdf.xacro
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/imu.urdf.xacro
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_front.urdf.xacro
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_rear.urdf.xacro
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/gazebo_sensor_plugin.xacro
[0.056s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config/ros2_control.yaml
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/custom_room.world
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room
[0.057s] -- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room/model.sdf
[0.066s] Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
