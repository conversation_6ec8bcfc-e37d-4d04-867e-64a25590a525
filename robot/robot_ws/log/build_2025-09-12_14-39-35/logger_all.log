[0.057s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.057s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x722fd284b340>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x722fd29a8d90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x722fd29a8d90>>)
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.154s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.154s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/robot/robot_ws'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.154s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.155s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(src/pointcloud_merger) by extension 'ros'
[0.170s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pointcloud_merger' with type 'ros.ament_cmake' and name 'pointcloud_merger'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.170s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_navigation2) by extension 'ros'
[0.171s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_navigation2' with type 'ros.ament_cmake' and name 'robot_navigation2'
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.171s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.183s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/robot/robot_ws/install
[0.183s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 376 installed packages in /opt/ros/humble
[0.185s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_target' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_clean_cache' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_clean_first' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'cmake_force_configure' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'ament_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'catkin_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'pointcloud_merger' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.207s] DEBUG:colcon.colcon_core.verb:Building package 'pointcloud_merger' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot/robot_ws/build/pointcloud_merger', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot/robot_ws/install/pointcloud_merger', 'merge_install': False, 'path': '/home/<USER>/robot/robot_ws/src/pointcloud_merger', 'symlink_install': False, 'test_result_base': None}
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.207s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot/robot_ws/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot/robot_ws/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/robot/robot_ws/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_target' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_clean_cache' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_clean_first' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'cmake_force_configure' from command line to 'False'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'ament_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'catkin_cmake_args' from command line to 'None'
[0.207s] Level 5:colcon.colcon_core.verb:set package 'robot_navigation2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.207s] DEBUG:colcon.colcon_core.verb:Building package 'robot_navigation2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot/robot_ws/build/robot_navigation2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot/robot_ws/install/robot_navigation2', 'merge_install': False, 'path': '/home/<USER>/robot/robot_ws/src/robot_navigation2', 'symlink_install': False, 'test_result_base': None}
[0.207s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.208s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.208s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot/robot_ws/src/pointcloud_merger' with build type 'ament_cmake'
[0.208s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot/robot_ws/src/pointcloud_merger'
[0.209s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.209s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.209s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.211s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot/robot_ws/src/robot_description' with build type 'ament_cmake'
[0.211s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot/robot_ws/src/robot_description'
[0.211s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.211s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.213s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot/robot_ws/src/robot_navigation2' with build type 'ament_cmake'
[0.213s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot/robot_ws/src/robot_navigation2'
[0.213s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.213s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.218s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.219s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.221s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.248s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_navigation2 -- -j20 -l20
[0.257s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_navigation2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.257s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/robot_description -- -j20 -l20
[0.258s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.267s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_navigation2)
[0.269s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_navigation2' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_navigation2
[0.269s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2' for CMake module files
[0.270s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2' for CMake config files
[0.270s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_navigation2', 'cmake_prefix_path')
[0.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.ps1'
[0.270s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.dsv'
[0.271s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.sh'
[0.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/bin'
[0.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/lib/pkgconfig/robot_navigation2.pc'
[0.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/lib/python3.10/site-packages'
[0.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/bin'
[0.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.ps1'
[0.272s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv'
[0.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.sh'
[0.273s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.bash'
[0.273s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.zsh'
[0.273s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_navigation2/share/colcon-core/packages/robot_navigation2)
[0.274s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_navigation2)
[0.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2' for CMake module files
[0.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2' for CMake config files
[0.274s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_navigation2', 'cmake_prefix_path')
[0.274s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.ps1'
[0.274s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.dsv'
[0.274s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/hook/cmake_prefix_path.sh'
[0.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/bin'
[0.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/lib/pkgconfig/robot_navigation2.pc'
[0.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/lib/python3.10/site-packages'
[0.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_navigation2/bin'
[0.275s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.ps1'
[0.275s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv'
[0.276s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.sh'
[0.276s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.bash'
[0.276s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.zsh'
[0.276s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_navigation2/share/colcon-core/packages/robot_navigation2)
[0.276s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.276s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.278s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.278s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/robot_description
[0.278s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.278s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.278s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.278s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.279s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.279s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.279s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.279s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.279s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.279s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.280s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.280s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.280s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake module files
[0.281s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description' for CMake config files
[0.281s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.281s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.281s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.281s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.281s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/lib/python3.10/site-packages'
[0.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/robot_description/bin'
[0.282s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.ps1'
[0.282s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv'
[0.282s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.sh'
[0.283s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.bash'
[0.283s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.zsh'
[0.283s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/robot_description/share/colcon-core/packages/robot_description)
[0.285s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/robot/robot_ws/build/pointcloud_merger -- -j20 -l20
[0.286s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.292s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pointcloud_merger)
[0.292s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot/robot_ws/build/pointcloud_merger' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/robot/robot_ws/build/pointcloud_merger
[0.293s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger' for CMake module files
[0.293s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger' for CMake config files
[0.293s] Level 1:colcon.colcon_core.shell:create_environment_hook('pointcloud_merger', 'cmake_prefix_path')
[0.293s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.ps1'
[0.294s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.dsv'
[0.294s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.sh'
[0.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib'
[0.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/bin'
[0.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pkgconfig/pointcloud_merger.pc'
[0.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/python3.10/site-packages'
[0.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/bin'
[0.295s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.ps1'
[0.295s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv'
[0.296s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.sh'
[0.296s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.bash'
[0.296s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.zsh'
[0.296s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/colcon-core/packages/pointcloud_merger)
[0.296s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pointcloud_merger)
[0.296s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger' for CMake module files
[0.297s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger' for CMake config files
[0.297s] Level 1:colcon.colcon_core.shell:create_environment_hook('pointcloud_merger', 'cmake_prefix_path')
[0.297s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.ps1'
[0.297s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.dsv'
[0.297s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/hook/cmake_prefix_path.sh'
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib'
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/bin'
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pkgconfig/pointcloud_merger.pc'
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/python3.10/site-packages'
[0.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot/robot_ws/install/pointcloud_merger/bin'
[0.298s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.ps1'
[0.298s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv'
[0.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.sh'
[0.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.bash'
[0.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.zsh'
[0.299s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot/robot_ws/install/pointcloud_merger/share/colcon-core/packages/pointcloud_merger)
[0.299s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.300s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.300s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.300s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.303s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.303s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.303s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.311s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.311s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.ps1'
[0.312s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_ps1.py'
[0.312s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.ps1'
[0.313s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.sh'
[0.313s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot/robot_ws/install/_local_setup_util_sh.py'
[0.314s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.sh'
[0.314s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.bash'
[0.314s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.bash'
[0.315s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot/robot_ws/install/local_setup.zsh'
[0.315s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot/robot_ws/install/setup.zsh'
