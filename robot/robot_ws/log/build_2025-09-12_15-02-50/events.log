[0.000000] (-) TimerEvent: {}
[0.000216] (pointcloud_merger) JobQueued: {'identifier': 'pointcloud_merger', 'dependencies': OrderedDict()}
[0.000246] (robot_description) JobQueued: {'identifier': 'robot_description', 'dependencies': OrderedDict()}
[0.000256] (robot_navigation2) JobQueued: {'identifier': 'robot_navigation2', 'dependencies': OrderedDict()}
[0.000643] (pointcloud_merger) JobStarted: {'identifier': 'pointcloud_merger'}
[0.004100] (robot_description) JobStarted: {'identifier': 'robot_description'}
[0.006275] (robot_navigation2) JobStarted: {'identifier': 'robot_navigation2'}
[0.008896] (pointcloud_merger) JobProgress: {'identifier': 'pointcloud_merger', 'progress': 'cmake'}
[0.009379] (pointcloud_merger) JobProgress: {'identifier': 'pointcloud_merger', 'progress': 'build'}
[0.009883] (pointcloud_merger) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/robot/robot_ws/build/pointcloud_merger', '--', '-j20', '-l20'], 'cwd': '/home/<USER>/robot/robot_ws/build/pointcloud_merger', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/pointcloud_merger'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.011226] (robot_description) JobProgress: {'identifier': 'robot_description', 'progress': 'cmake'}
[0.011464] (robot_description) JobProgress: {'identifier': 'robot_description', 'progress': 'build'}
[0.011653] (robot_description) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/robot/robot_ws/build/robot_description', '--', '-j20', '-l20'], 'cwd': '/home/<USER>/robot/robot_ws/build/robot_description', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/robot_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.014397] (robot_navigation2) JobProgress: {'identifier': 'robot_navigation2', 'progress': 'cmake'}
[0.014840] (robot_navigation2) JobProgress: {'identifier': 'robot_navigation2', 'progress': 'build'}
[0.015022] (robot_navigation2) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/robot/robot_ws/build/robot_navigation2', '--', '-j20', '-l20'], 'cwd': '/home/<USER>/robot/robot_ws/build/robot_navigation2', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/robot_navigation2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.042464] (robot_navigation2) CommandEnded: {'returncode': 0}
[0.043307] (robot_navigation2) JobProgress: {'identifier': 'robot_navigation2', 'progress': 'install'}
[0.050746] (robot_navigation2) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/robot/robot_ws/build/robot_navigation2'], 'cwd': '/home/<USER>/robot/robot_ws/build/robot_navigation2', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/robot_navigation2'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.052585] (robot_description) CommandEnded: {'returncode': 0}
[0.053195] (robot_description) JobProgress: {'identifier': 'robot_description', 'progress': 'install'}
[0.053501] (robot_description) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/robot/robot_ws/build/robot_description'], 'cwd': '/home/<USER>/robot/robot_ws/build/robot_description', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/robot_description'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.055024] (pointcloud_merger) StdoutLine: {'line': b'[ 25%] Built target imu_wheel_odom_node\n'}
[0.057417] (robot_navigation2) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.057667] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch\n'}
[0.057780] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/local_navigation.launch.py\n'}
[0.057853] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/launch/navigation2.launch.py\n'}
[0.058134] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps\n'}
[0.058323] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.pgm\n'}
[0.058425] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/maps/room.yaml\n'}
[0.058523] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config\n'}
[0.058772] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/nav2_params.yaml\n'}
[0.058869] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/config/local_nav_rviz.rviz\n'}
[0.058970] (pointcloud_merger) StdoutLine: {'line': b'[ 50%] Built target pointcloud_merger_node\n'}
[0.059071] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/package_run_dependencies/robot_navigation2\n'}
[0.059163] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/parent_prefix_path/robot_navigation2\n'}
[0.059252] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.sh\n'}
[0.059342] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/ament_prefix_path.dsv\n'}
[0.059430] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.sh\n'}
[0.059514] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/environment/path.dsv\n'}
[0.059655] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.bash\n'}
[0.060385] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.sh\n'}
[0.060510] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.zsh\n'}
[0.060544] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/local_setup.dsv\n'}
[0.060572] (robot_navigation2) StdoutLine: {'line': b'-- Installing: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.dsv\n'}
[0.060599] (robot_description) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.061134] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/package_run_dependencies/robot_description\n'}
[0.061166] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/parent_prefix_path/robot_description\n'}
[0.061193] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.sh\n'}
[0.061236] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/ament_prefix_path.dsv\n'}
[0.061262] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.sh\n'}
[0.061288] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/environment/path.dsv\n'}
[0.061313] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.bash\n'}
[0.061338] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.sh\n'}
[0.061363] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.zsh\n'}
[0.061415] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/local_setup.dsv\n'}
[0.061443] (robot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.dsv\n'}
[0.061469] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/ament_index/resource_index/packages/robot_description\n'}
[0.061494] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig.cmake\n'}
[0.061520] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/cmake/robot_descriptionConfig-version.cmake\n'}
[0.061547] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/ament_index/resource_index/packages/robot_navigation2\n'}
[0.061575] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config.cmake\n'}
[0.061601] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/cmake/robot_navigation2Config-version.cmake\n'}
[0.061626] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/package.xml\n'}
[0.061651] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch\n'}
[0.061677] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/launch/gazebo.launch.py\n'}
[0.061702] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf\n'}
[0.061727] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot\n'}
[0.061752] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/robot.urdf.xacro\n'}
[0.061776] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator\n'}
[0.061800] (robot_description) StdoutLine: {'line': b'-- Installing: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/actuator/wheel.urdf.xacro\n'}
[0.061825] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/base.urdf.xacro\n'}
[0.061849] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor\n'}
[0.061874] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/imu.urdf.xacro\n'}
[0.061917] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_front.urdf.xacro\n'}
[0.061944] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/laser_rear.urdf.xacro\n'}
[0.061969] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/urdf/robot/sensor/gazebo_sensor_plugin.xacro\n'}
[0.062005] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config\n'}
[0.062029] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/config/ros2_control.yaml\n'}
[0.062100] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world\n'}
[0.062126] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/custom_room.world\n'}
[0.062152] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room\n'}
[0.062177] (robot_description) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_description/share/robot_description/world/room/model.sdf\n'}
[0.062203] (robot_navigation2) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/robot_navigation2/share/robot_navigation2/package.xml\n'}
[0.062231] (pointcloud_merger) StdoutLine: {'line': b'[ 75%] Built target pointcloud_to_laserscan_node\n'}
[0.062295] (robot_navigation2) CommandEnded: {'returncode': 0}
[0.062564] (pointcloud_merger) StdoutLine: {'line': b'[100%] Built target nav_laser_converter\n'}
[0.071844] (robot_navigation2) JobEnded: {'identifier': 'robot_navigation2', 'rc': 0}
[0.072493] (robot_description) CommandEnded: {'returncode': 0}
[0.080322] (robot_description) JobEnded: {'identifier': 'robot_description', 'rc': 0}
[0.081507] (pointcloud_merger) CommandEnded: {'returncode': 0}
[0.081860] (pointcloud_merger) JobProgress: {'identifier': 'pointcloud_merger', 'progress': 'install'}
[0.082126] (pointcloud_merger) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/robot/robot_ws/build/pointcloud_merger'], 'cwd': '/home/<USER>/robot/robot_ws/build/pointcloud_merger', 'env': OrderedDict([('PYTHON_BASIC_REPL', '1'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'fml'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/local/cuda-12.1/lib64:/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/usr/local/cuda-12.1/lib64'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/robot/robot_ws/src'), ('TERM_PROGRAM_VERSION', '1.103.2'), ('DESKTOP_SESSION', 'ubuntu'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.18.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.18.0/include/node'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1840'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '2251'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '595791'), ('NVM_DIR', '/home/<USER>/.nvm'), ('IM_CONFIG_PHASE', '1'), ('COLCON_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'fml'), ('JOURNAL_STREAM', '8:24899'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'fml'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '1'), ('WINDOWPATH', '2'), ('PATH', '/usr/local/cuda-12.1/bin:/home/<USER>/.local/bin:/home/<USER>/.nvm/versions/node/v22.18.0/bin:/opt/ros/humble/bin:/usr/local/cuda-12.1/bin:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand'), ('SESSION_MANAGER', 'local/fml-ROG-Strix-G533ZW-G533ZW:@/tmp/.ICE-unix/2251,unix/fml-ROG-Strix-G533ZW-G533ZW:/tmp/.ICE-unix/2251'), ('INVOCATION_ID', '30d5cbcc49b3401382c208ae0b41ca11'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-cb50a75093ee0f3a.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('PYTHONSTARTUP', '/home/<USER>/.config/Code/User/workspaceStorage/bf7378a885d859133017f1248a4db012/ms-python.python/pythonrc.py'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2f1dd6b6b3.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GAZEBO_IP', '127.0.0.1'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/robot/robot_ws/build/pointcloud_merger'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('GEMINI_API_KEY', '“AIzaSyD7ky3HweZl8-eBb_uyOOKcwc1DLpFYZL8”'), ('CLUTTER_IM_MODULE', 'xim'), ('NVM_CD_FLAGS', ''), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=00bfef81f393027b531e612268c1395a'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/share/gnome:/home/<USER>/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/robot/robot_ws/install/robot_navigation2:/home/<USER>/robot/robot_ws/install/robot_description:/home/<USER>/robot/robot_ws/install/pointcloud_merger:/opt/ros/humble')]), 'shell': False}
[0.091105] (pointcloud_merger) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.091283] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_merger_node\n'}
[0.091446] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/pointcloud_to_laserscan_node\n'}
[0.091665] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/nav_laser_converter\n'}
[0.091964] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/lib/pointcloud_merger/imu_wheel_odom_node\n'}
[0.092790] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch\n'}
[0.092845] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/nav_laser.launch.py\n'}
[0.092876] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/imu_wheel_odom.launch.py\n'}
[0.092906] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/launch/merger.launch.py\n'}
[0.092935] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/package_run_dependencies/pointcloud_merger\n'}
[0.092965] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/parent_prefix_path/pointcloud_merger\n'}
[0.092997] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.sh\n'}
[0.093024] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/ament_prefix_path.dsv\n'}
[0.093051] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.sh\n'}
[0.093088] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/environment/path.dsv\n'}
[0.093114] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.bash\n'}
[0.093137] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.sh\n'}
[0.093160] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.zsh\n'}
[0.093185] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/local_setup.dsv\n'}
[0.093208] (pointcloud_merger) StdoutLine: {'line': b'-- Installing: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.dsv\n'}
[0.093232] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/ament_index/resource_index/packages/pointcloud_merger\n'}
[0.093255] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig.cmake\n'}
[0.093278] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/cmake/pointcloud_mergerConfig-version.cmake\n'}
[0.093309] (pointcloud_merger) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/robot/robot_ws/install/pointcloud_merger/share/pointcloud_merger/package.xml\n'}
[0.094266] (pointcloud_merger) CommandEnded: {'returncode': 0}
[0.100035] (-) TimerEvent: {}
[0.101115] (pointcloud_merger) JobEnded: {'identifier': 'pointcloud_merger', 'rc': 0}
[0.101409] (-) EventReactorShutdown: {}
